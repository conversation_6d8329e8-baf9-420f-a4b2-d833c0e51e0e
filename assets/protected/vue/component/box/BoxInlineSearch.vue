<template>
  <div>
    <div id="inlineSearch">
      <div class="row">
        <div class="col-6">
          <typeahead-input
            :src="'/box/api/search?folder=' + folderPath + '&language=' + languageTld"
            query-param-name="search"
            :placeholder="t('box/search/placeholder')"
            :responseAttr="['cleanName', 'extension', 'flags', 'cleanPath']"
            :disableReset="true"
            :limit="50"
            :limitText="t('box/search/limited')"
            :nothingFoundText="t('box/search/nothing_found')"
            @hit="onSelectFile"
            cssStyle="box"></typeahead-input>
        </div>

        <div class="col-3">
          <div class="dropdown">
            <button
              id="dropdownLanguage"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="languageFlag + language"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownLanguage">
              <a
                class="dropdown-item"
                href="#"
                style="font-weight: bold"
                @click="selectLanguage({ name: t('box/search/all_languages'), tld: null })">
                {{ t('box/search/all_languages') }}
              </a>
              <a
                v-for="languageItem in languages"
                :key="languageItem.tld"
                class="dropdown-item"
                href="#"
                @click="selectLanguage(languageItem)">
                <img class="flag" :src="getFlagPath(languageItem.tld)" />&nbsp;&nbsp;{{ languageItem.name }}
              </a>
            </div>
          </div>
        </div>

        <div class="col-3">
          <div class="dropdown">
            <button
              id="dropdownFolder"
              class="btn btn-sm btn-block btn-light dropdown-toggle"
              data-toggle="dropdown"
              type="button"
              aria-haspopup="true"
              aria-expanded="false"
              v-html="folder"></button>
            <div class="dropdown-menu" aria-labelledby="dropdownFolder">
              <a
                class="dropdown-item"
                href="#"
                style="font-weight: bold"
                @click="selectFolder({ name: t('box/search/all_folders'), urlpath: '/' })">
                {{ t('box/search/all_folders') }}
              </a>
              <a
                v-for="folderItem in folders"
                :key="folderItem.urlpath"
                class="dropdown-item"
                href="#"
                @click="selectFolder(folderItem)"
                v-html="folderItem.name"></a>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!--<div v-if="chosenFile !== null" id="file">-->
    <!--<div class="filename" v-html="chosenFile.cleanName + '.' + chosenFile.extension + chosenFile.flags">-->

    <!--</div>-->
    <!--<div class="buttons">-->
    <!--<button class="btn btn-success btn-sm">Datei herunterladen</button>-->
    <!--<button class="btn btn-info btn-sm">Datei azeigen</button>-->
    <!--<button class="btn btn-warning btn-sm" @click="openFolder(chosenFile.path)">Ordner öffnen</button>-->
    <!--</div>-->
    <!--</div>-->
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, getCurrentInstance } from 'vue';
import TypeaheadInput from '@assets/protected/vue/component/helper/TypeaheadInput.vue';
import { store } from '@assets/protected/vue/store';

// import BoxEventBus from 'EventBuses/box';

interface Props {
  startFolder?: string | null;
  folders?: Array<any>;
  languages?: Array<any>;
}

const props = withDefaults(defineProps<Props>(), {
  startFolder: null,
  folders: () => [],
  languages: () => [],
});

const { proxy } = getCurrentInstance()!;
const t = proxy.$t;

const query = ref<string>('');
const folder = ref<string>('');
const folderPath = ref<string>('');
const language = ref<string>('');
const languageTld = ref<string>('');
const languageFlag = ref<string>('');

// chosenFile: any = null;

/**
 * Callback Funktion die aufgerufen wird, wenn auf ein Suchergebnis geklickt wird
 *
 * @param file
 */
const onSelectFile = (file: any): void => {
  store.dispatch('box/openFolder', file.path);
  // BoxEventBus.$emit('open', file.path);
};

const setQuery = (queryValue: string): void => {
  query.value = queryValue;
};

const getFlagPath = (tld: string): string => {
  const flagTld = tld.replace('en', 'gb').replace('eu', 'aq');
  return `/dist/images/flags/png100px/${flagTld}.png`;
};

const selectLanguage = (selectedLanguage: any): void => {
  language.value = selectedLanguage.name;
  languageTld.value = selectedLanguage.tld;
  languageFlag.value =
    selectedLanguage.tld !== null ? `<img src="/dist/images/flags/${selectedLanguage.tld}.png">` : '';
};

const selectFolder = (selectedFolder: any): void => {
  folder.value = selectedFolder.name;
  folderPath.value = selectedFolder.urlpath;
};

onMounted(() => {
  folderPath.value = props.startFolder !== null ? props.startFolder : '/';

  folder.value = t('box/search/all_folders') as string;
  language.value = t('box/search/all_languages') as string;
});
</script>

<style lang="scss" scoped>
@import '@assets/public/vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables.scss';

#inlineSearch {
  padding: 15px 10px;
  background-color: $abus-grey-7;
  color: $white;
  border-top-left-radius: 0.3em;
  border-top-right-radius: 0.3em;

  .dropdown {
    button {
      height: 37px;
    }
    img {
      margin-top: -5px;
    }
  }
}

#file {
  padding: 15px 10px;
  border-top: #dfdfdf 1px solid;
  background-color: $abus-blue-light;
  color: $white;
  display: flow-root;

  div.filename {
    float: left;
    margin-right: 20px;
    line-height: 30px;
  }

  div.buttons {
    float: right;
  }

  button {
    margin-left: 15px;
  }
}
</style>

<style lang="scss">
div#inlineSearch .dropdown button img {
  margin-top: -3px;
  margin-right: 10px;
}

div#inlineSearch .flag {
  width: 20px;
  height: 13px;
}
</style>
