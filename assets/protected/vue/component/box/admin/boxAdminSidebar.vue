<template>
  <div id="sideSearch" class="sidebar-panel">
    <h5 class="sidebar-panel-title">
      <i class="fas fa-cloud-download-alt mr5"></i>{{ $t('box/admin/administration') }}
    </h5>

    <div class="content">
      <button
        class="btn btn-sm btn-abus-blue-dark btn-block"
        type="button"
        @click="createIndexTask({ path: '/', pdf: false })"
        v-html="$t('box/admin/index')"></button>
      <button
        class="btn btn-sm btn-danger btn-block"
        type="button"
        @click="createIndexTask({ path: '/', pdf: true })"
        v-html="$t('box/admin/index_pdf')">
        $t("box/admin/index_pdf")<br /><span style="font-size: 0.8em; font-weight: bold"
          >$t("box/admin/takeAWhile")</span
        >
      </button>

      <div class="mt10">
        <i class="far fa-hdd mr5"></i>
        {{ $t('box/admin/diskSpace') }}: {{ datasize }}
      </div>
    </div>
  </div>
</template>

<script lang="ts">
// var qs = require('qs');

import Axios from 'axios';
import Vue from 'vue';
import Component from 'vue-class-component';
// import {Prop, Watch} from 'vue-property-decorator';
import { Action } from 'vuex-class';

import ModalEventBus from 'EventBuses/modal';

import de from 'Translations/across/across.de_DE.json';
import en from 'Translations/across/across.en_GB.json';
import fr from 'Translations/across/across.fr_FR.json';
import es from 'Translations/across/across.es_ES.json';

import VueI18n from 'vue-i18n';
Vue.use(VueI18n);

const i18n = new VueI18n({
  locale: <string>document.getElementsByTagName('html')[0].getAttribute('lang'),
  messages: { de, en, fr, es },
});

// administration: 'Administration',
//     takeAWhile: 'This may take a while!',
//     diskSpace: 'Disk space',

@Component({
  name: 'BoxAdminSidebar',
  i18n,
  components: {},
})
export default class BoxAdminSidebar extends Vue {
  @Action('box/createIndexTask') createIndexTask: any;

  datasize: string = '';

  public getDataSize(): void {
    let self = this;

    let url = '/box/api/administration/getDataSize';

    Axios.get(url)
      .then((response: any) => {
        self.datasize = response.data;
      })
      .catch((error: any) => {
        ModalEventBus.$emit('showModal', { type: 'error', message: error.response.data });
      });
  }

  private created() {
    this.getDataSize();
  }
}
</script>

<style lang="scss" scoped>
@import '@assets/public/vendor/dynamicTheme/css/bootstrapCustomScss/custom-variables.scss';
</style>
